# MCP Chrome Bridge STDIO 配置指南

## 🎯 概述
本指南帮助您为 MCP Chrome Bridge 生成正确的 STDIO 配置文件。

## 🚀 快速开始

### 方法1: 使用自动化脚本（推荐）

#### Windows 用户
```bash
# 运行批处理脚本
quick_config.bat
```

#### Linux/macOS 用户
```bash
# 给脚本执行权限
chmod +x quick_config.sh

# 运行脚本
./quick_config.sh
```

#### Node.js 用户
```bash
# 运行 Node.js 脚本
node generate_mcp_config.js
```

### 方法2: 手动查找和配置

#### 步骤1: 查找安装路径

**使用 npm:**
```bash
npm list -g mcp-chrome-bridge
```

**使用 pnpm:**
```bash
pnpm list -g mcp-chrome-bridge
```

**获取全局路径:**
```bash
# npm 方式
npm root -g

# pnpm 方式  
pnpm root -g
```

#### 步骤2: 构建完整路径
假设全局路径是 `/usr/local/lib/node_modules`，那么完整路径就是：
```
/usr/local/lib/node_modules/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js
```

#### 步骤3: 生成配置

## 📝 配置模板

### 配置选项1: 直接使用 node（推荐）
```json
{
  "mcpServers": {
    "chrome-mcp-stdio": {
      "command": "node",
      "args": [
        "/your/path/to/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js"
      ]
    }
  }
}
```

### 配置选项2: 使用 npx
```json
{
  "mcpServers": {
    "chrome-mcp-stdio": {
      "command": "npx",
      "args": [
        "node",
        "/your/path/to/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js"
      ]
    }
  }
}
```

### 配置选项3: Streamable HTTP（最推荐）
```json
{
  "mcpServers": {
    "chrome-mcp-server": {
      "type": "streamableHttp",
      "url": "http://127.0.0.1:12306/mcp"
    }
  }
}
```

## 🔍 常见安装路径

### Windows
```
# npm
C:\Users\<USER>\AppData\Roaming\npm\node_modules\mcp-chrome-bridge\dist\mcp\mcp-server-stdio.js

# pnpm
C:\Users\<USER>\AppData\Local\pnpm\global\5\node_modules\mcp-chrome-bridge\dist\mcp\mcp-server-stdio.js
```

### macOS
```
# npm
/usr/local/lib/node_modules/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js

# pnpm
/Users/<USER>/Library/pnpm/global/5/node_modules/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js
```

### Linux
```
# npm
/usr/local/lib/node_modules/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js

# pnpm
/home/<USER>/.local/share/pnpm/global/5/node_modules/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js
```

## 🛠️ 不同客户端配置示例

### Augment 配置
在 Augment 的 MCP 设置中添加：
```json
{
  "mcpServers": {
    "chrome-mcp-stdio": {
      "command": "node",
      "args": ["/your/actual/path/to/mcp-server-stdio.js"]
    }
  }
}
```

### Claude Desktop 配置
编辑 `~/.claude/claude_desktop_config.json`:
```json
{
  "mcpServers": {
    "chrome-mcp-stdio": {
      "command": "node",
      "args": ["/your/actual/path/to/mcp-server-stdio.js"]
    }
  }
}
```

### Continue 配置
在 `config.json` 中添加：
```json
{
  "mcpServers": {
    "chrome-mcp-stdio": {
      "command": "node",
      "args": ["/your/actual/path/to/mcp-server-stdio.js"]
    }
  }
}
```

## ✅ 验证配置

### 1. 检查文件存在
```bash
# 检查文件是否存在
ls -la /your/path/to/mcp-server-stdio.js

# Windows
dir "C:\your\path\to\mcp-server-stdio.js"
```

### 2. 测试运行
```bash
# 直接运行测试
node /your/path/to/mcp-server-stdio.js

# 应该看到 MCP 服务器启动信息
```

### 3. Chrome 扩展检查
1. 打开 Chrome 扩展页面 `chrome://extensions/`
2. 确保 Chrome MCP Server 扩展已启用
3. 点击扩展图标，确保显示"已连接"状态

## 🚨 故障排除

### 问题1: 找不到安装路径
**解决方案:**
```bash
# 重新安装
npm uninstall -g mcp-chrome-bridge
npm install -g mcp-chrome-bridge

# 或使用 pnpm
pnpm uninstall -g mcp-chrome-bridge  
pnpm install -g mcp-chrome-bridge
```

### 问题2: 权限错误
**解决方案:**
```bash
# Linux/macOS 添加执行权限
chmod +x /path/to/mcp-server-stdio.js

# Windows 以管理员身份运行
```

### 问题3: 端口冲突
**解决方案:**
- 检查端口 12306 是否被占用
- 重启 Chrome 扩展
- 重启浏览器

## 📞 获取帮助

如果遇到问题，请：
1. 检查 Chrome 扩展控制台错误
2. 查看 MCP 客户端日志
3. 确认 Node.js 版本 >= 18.19.0
4. 参考项目 GitHub Issues: https://github.com/hangwin/mcp-chrome/issues

## 🎉 配置完成

配置成功后，您就可以在支持 MCP 的 AI 客户端中使用 Chrome 浏览器自动化功能了！

主要功能包括：
- 🌐 浏览器标签页管理
- 📸 网页截图
- 🔍 内容分析和搜索  
- 🎯 元素交互操作
- 📚 书签和历史管理
- 🌐 网络请求监控
