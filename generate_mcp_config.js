#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('🔍 正在查找 mcp-chrome-bridge 安装路径...\n');

/**
 * 查找npm全局包路径
 */
function findNpmGlobalPath() {
    try {
        const result = execSync('npm list -g mcp-chrome-bridge', { encoding: 'utf8' });
        console.log('📦 npm 查找结果:');
        console.log(result);
        
        // 提取路径信息
        const lines = result.split('\n');
        const globalPath = lines[0].trim();
        if (globalPath && globalPath !== '') {
            return path.join(globalPath, 'node_modules', 'mcp-chrome-bridge', 'dist', 'mcp', 'mcp-server-stdio.js');
        }
    } catch (error) {
        console.log('❌ npm 方式未找到 mcp-chrome-bridge');
        return null;
    }
}

/**
 * 查找pnpm全局包路径
 */
function findPnpmGlobalPath() {
    try {
        const result = execSync('pnpm list -g mcp-chrome-bridge', { encoding: 'utf8' });
        console.log('📦 pnpm 查找结果:');
        console.log(result);
        
        // 提取路径信息
        const lines = result.split('\n');
        for (const line of lines) {
            if (line.includes('mcp-chrome-bridge')) {
                // 尝试从输出中提取路径
                const match = line.match(/(.+)\/node_modules/);
                if (match) {
                    return path.join(match[1], 'node_modules', 'mcp-chrome-bridge', 'dist', 'mcp', 'mcp-server-stdio.js');
                }
            }
        }
        
        // 如果上面方法失败，尝试获取pnpm全局路径
        const globalRoot = execSync('pnpm root -g', { encoding: 'utf8' }).trim();
        return path.join(globalRoot, 'mcp-chrome-bridge', 'dist', 'mcp', 'mcp-server-stdio.js');
    } catch (error) {
        console.log('❌ pnpm 方式未找到 mcp-chrome-bridge');
        return null;
    }
}

/**
 * 尝试常见的安装路径
 */
function findCommonPaths() {
    const commonPaths = [
        // npm 常见路径
        path.join(os.homedir(), '.npm-global', 'node_modules', 'mcp-chrome-bridge', 'dist', 'mcp', 'mcp-server-stdio.js'),
        path.join('/usr/local/lib/node_modules', 'mcp-chrome-bridge', 'dist', 'mcp', 'mcp-server-stdio.js'),
        
        // pnpm 常见路径
        path.join(os.homedir(), '.local/share/pnpm/global/5/node_modules', 'mcp-chrome-bridge', 'dist', 'mcp', 'mcp-server-stdio.js'),
        path.join(os.homedir(), 'Library/pnpm/global/5/node_modules', 'mcp-chrome-bridge', 'dist', 'mcp', 'mcp-server-stdio.js'),
        
        // Windows 路径
        path.join(os.homedir(), 'AppData/Roaming/npm/node_modules', 'mcp-chrome-bridge', 'dist', 'mcp', 'mcp-server-stdio.js'),
    ];
    
    console.log('🔍 检查常见安装路径...');
    for (const testPath of commonPaths) {
        console.log(`检查: ${testPath}`);
        if (fs.existsSync(testPath)) {
            console.log(`✅ 找到文件: ${testPath}`);
            return testPath;
        }
    }
    
    return null;
}

/**
 * 生成MCP配置
 */
function generateMcpConfig(serverPath) {
    const config = {
        mcpServers: {
            "chrome-mcp-stdio": {
                command: "node",
                args: [serverPath]
            }
        }
    };
    
    return JSON.stringify(config, null, 2);
}

/**
 * 生成多种配置选项
 */
function generateAllConfigs(serverPath) {
    console.log('\n📝 生成配置文件...\n');
    
    // 配置选项1: 直接使用node
    const config1 = {
        mcpServers: {
            "chrome-mcp-stdio": {
                command: "node",
                args: [serverPath]
            }
        }
    };
    
    // 配置选项2: 使用npx
    const config2 = {
        mcpServers: {
            "chrome-mcp-stdio": {
                command: "npx",
                args: ["node", serverPath]
            }
        }
    };
    
    // 配置选项3: Streamable HTTP (推荐)
    const config3 = {
        mcpServers: {
            "chrome-mcp-server": {
                type: "streamableHttp",
                url: "http://127.0.0.1:12306/mcp"
            }
        }
    };
    
    return { config1, config2, config3 };
}

// 主执行逻辑
function main() {
    console.log('🚀 MCP Chrome Bridge 配置生成器\n');
    
    // 尝试查找安装路径
    let serverPath = findNpmGlobalPath() || findPnpmGlobalPath() || findCommonPaths();
    
    if (!serverPath) {
        console.log('❌ 未找到 mcp-chrome-bridge 安装路径');
        console.log('\n💡 请确保已安装 mcp-chrome-bridge:');
        console.log('   npm install -g mcp-chrome-bridge');
        console.log('   或');
        console.log('   pnpm install -g mcp-chrome-bridge');
        console.log('\n🔧 或者手动指定路径:');
        console.log('   node generate_mcp_config.js /path/to/mcp-server-stdio.js');
        return;
    }
    
    // 验证文件是否存在
    if (!fs.existsSync(serverPath)) {
        console.log(`❌ 文件不存在: ${serverPath}`);
        return;
    }
    
    console.log(`✅ 找到 mcp-server-stdio.js: ${serverPath}\n`);
    
    // 生成配置
    const configs = generateAllConfigs(serverPath);
    
    // 保存配置文件
    const configFiles = [
        { name: 'mcp-config-node.json', config: configs.config1, desc: '直接使用 node 命令' },
        { name: 'mcp-config-npx.json', config: configs.config2, desc: '使用 npx 命令' },
        { name: 'mcp-config-http.json', config: configs.config3, desc: 'Streamable HTTP 方式（推荐）' }
    ];
    
    configFiles.forEach(({ name, config, desc }) => {
        const content = JSON.stringify(config, null, 2);
        fs.writeFileSync(name, content);
        console.log(`📄 已生成: ${name} (${desc})`);
        console.log(`内容预览:\n${content}\n`);
    });
    
    console.log('🎉 配置文件生成完成！');
    console.log('\n📋 使用说明:');
    console.log('1. 选择合适的配置文件复制到您的 MCP 客户端');
    console.log('2. 推荐使用 mcp-config-http.json (Streamable HTTP 方式)');
    console.log('3. 如果客户端不支持 HTTP，使用 mcp-config-node.json');
    console.log('\n🔗 确保 Chrome 扩展已安装并连接成功！');
}

// 支持命令行参数指定路径
if (process.argv[2]) {
    const manualPath = process.argv[2];
    if (fs.existsSync(manualPath)) {
        console.log(`✅ 使用手动指定路径: ${manualPath}`);
        const configs = generateAllConfigs(manualPath);
        console.log('\n📝 生成的配置:');
        console.log(JSON.stringify(configs.config1, null, 2));
    } else {
        console.log(`❌ 指定的路径不存在: ${manualPath}`);
    }
} else {
    main();
}
