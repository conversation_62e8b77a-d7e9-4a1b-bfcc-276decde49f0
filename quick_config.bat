@echo off
chcp 65001 >nul
echo 🚀 MCP Chrome Bridge 配置生成器
echo.

echo 🔍 正在查找 mcp-chrome-bridge 安装路径...
echo.

REM 尝试npm方式
echo 📦 检查 npm 全局安装...
npm list -g mcp-chrome-bridge 2>nul
if %errorlevel% equ 0 (
    echo ✅ 通过 npm 找到 mcp-chrome-bridge
    for /f "tokens=1" %%i in ('npm root -g 2^>nul') do set NPM_GLOBAL=%%i
    set STDIO_PATH=%NPM_GLOBAL%\mcp-chrome-bridge\dist\mcp\mcp-server-stdio.js
    goto :generate_config
)

REM 尝试pnpm方式
echo 📦 检查 pnpm 全局安装...
pnpm list -g mcp-chrome-bridge 2>nul
if %errorlevel% equ 0 (
    echo ✅ 通过 pnpm 找到 mcp-chrome-bridge
    for /f "tokens=1" %%i in ('pnpm root -g 2^>nul') do set PNPM_GLOBAL=%%i
    set STDIO_PATH=%PNPM_GLOBAL%\mcp-chrome-bridge\dist\mcp\mcp-server-stdio.js
    goto :generate_config
)

echo ❌ 未找到 mcp-chrome-bridge 安装
echo.
echo 💡 请先安装 mcp-chrome-bridge:
echo    npm install -g mcp-chrome-bridge
echo    或
echo    pnpm install -g mcp-chrome-bridge
echo.
pause
exit /b 1

:generate_config
echo.
echo ✅ 找到路径: %STDIO_PATH%
echo.

REM 检查文件是否存在
if not exist "%STDIO_PATH%" (
    echo ❌ 文件不存在: %STDIO_PATH%
    echo 请检查安装是否完整
    pause
    exit /b 1
)

echo 📝 生成配置文件...
echo.

REM 生成配置1: 使用node命令
(
echo {
echo   "mcpServers": {
echo     "chrome-mcp-stdio": {
echo       "command": "node",
echo       "args": [
echo         "%STDIO_PATH:\=\\%"
echo       ]
echo     }
echo   }
echo }
) > mcp-config-node.json

REM 生成配置2: 使用npx命令
(
echo {
echo   "mcpServers": {
echo     "chrome-mcp-stdio": {
echo       "command": "npx",
echo       "args": [
echo         "node",
echo         "%STDIO_PATH:\=\\%"
echo       ]
echo     }
echo   }
echo }
) > mcp-config-npx.json

REM 生成配置3: HTTP方式（推荐）
(
echo {
echo   "mcpServers": {
echo     "chrome-mcp-server": {
echo       "type": "streamableHttp",
echo       "url": "http://127.0.0.1:12306/mcp"
echo     }
echo   }
echo }
) > mcp-config-http.json

echo 📄 已生成配置文件:
echo    ✅ mcp-config-node.json (直接使用 node)
echo    ✅ mcp-config-npx.json (使用 npx)  
echo    ✅ mcp-config-http.json (HTTP方式，推荐)
echo.

echo 📋 配置内容预览:
echo.
echo 🔹 STDIO 配置 (node方式):
type mcp-config-node.json
echo.
echo.
echo 🔹 HTTP 配置 (推荐):
type mcp-config-http.json
echo.

echo 🎉 配置生成完成！
echo.
echo 📖 使用说明:
echo 1. 推荐使用 mcp-config-http.json (HTTP方式)
echo 2. 如果客户端不支持HTTP，使用 mcp-config-node.json
echo 3. 将配置内容复制到您的MCP客户端配置中
echo 4. 确保Chrome扩展已安装并连接成功
echo.
pause
