#!/bin/bash

echo "🚀 MCP Chrome Bridge 配置生成器"
echo ""

echo "🔍 正在查找 mcp-chrome-bridge 安装路径..."
echo ""

# 尝试npm方式
echo "📦 检查 npm 全局安装..."
if npm list -g mcp-chrome-bridge >/dev/null 2>&1; then
    echo "✅ 通过 npm 找到 mcp-chrome-bridge"
    NPM_GLOBAL=$(npm root -g 2>/dev/null)
    STDIO_PATH="$NPM_GLOBAL/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js"
elif pnpm list -g mcp-chrome-bridge >/dev/null 2>&1; then
    echo "✅ 通过 pnpm 找到 mcp-chrome-bridge"
    PNPM_GLOBAL=$(pnpm root -g 2>/dev/null)
    STDIO_PATH="$PNPM_GLOBAL/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js"
else
    echo "❌ 未找到 mcp-chrome-bridge 安装"
    echo ""
    echo "💡 请先安装 mcp-chrome-bridge:"
    echo "   npm install -g mcp-chrome-bridge"
    echo "   或"
    echo "   pnpm install -g mcp-chrome-bridge"
    echo ""
    exit 1
fi

echo ""
echo "✅ 找到路径: $STDIO_PATH"
echo ""

# 检查文件是否存在
if [ ! -f "$STDIO_PATH" ]; then
    echo "❌ 文件不存在: $STDIO_PATH"
    echo "请检查安装是否完整"
    exit 1
fi

echo "📝 生成配置文件..."
echo ""

# 生成配置1: 使用node命令
cat > mcp-config-node.json << EOF
{
  "mcpServers": {
    "chrome-mcp-stdio": {
      "command": "node",
      "args": [
        "$STDIO_PATH"
      ]
    }
  }
}
EOF

# 生成配置2: 使用npx命令
cat > mcp-config-npx.json << EOF
{
  "mcpServers": {
    "chrome-mcp-stdio": {
      "command": "npx",
      "args": [
        "node",
        "$STDIO_PATH"
      ]
    }
  }
}
EOF

# 生成配置3: HTTP方式（推荐）
cat > mcp-config-http.json << EOF
{
  "mcpServers": {
    "chrome-mcp-server": {
      "type": "streamableHttp",
      "url": "http://127.0.0.1:12306/mcp"
    }
  }
}
EOF

echo "📄 已生成配置文件:"
echo "   ✅ mcp-config-node.json (直接使用 node)"
echo "   ✅ mcp-config-npx.json (使用 npx)"
echo "   ✅ mcp-config-http.json (HTTP方式，推荐)"
echo ""

echo "📋 配置内容预览:"
echo ""
echo "🔹 STDIO 配置 (node方式):"
cat mcp-config-node.json
echo ""
echo ""
echo "🔹 HTTP 配置 (推荐):"
cat mcp-config-http.json
echo ""

echo "🎉 配置生成完成！"
echo ""
echo "📖 使用说明:"
echo "1. 推荐使用 mcp-config-http.json (HTTP方式)"
echo "2. 如果客户端不支持HTTP，使用 mcp-config-node.json"
echo "3. 将配置内容复制到您的MCP客户端配置中"
echo "4. 确保Chrome扩展已安装并连接成功"
echo ""
